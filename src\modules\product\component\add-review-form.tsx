'use client'

import { useState, useEffect, FormEvent } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Edit, X } from 'lucide-react'
import { FileUpload } from "./fileupload"
import { IPackageReview } from "@/types/package_"
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image"

const initialFormState = {
    id: "",
    packageId: "",
    name: "",
    email: "",
    rating: 0,
    reviewImage: "",
    comment: "",
    published: false,
}

export function AddReviewForm({
    editingItem,
    onAdd,
    onUpdate,
    onCancelEdit
}: {
    editingItem: IPackageReview | null,
    onAdd: (data: Omit<IPackageReview, 'id'>) => void,
    onUpdate: (data: IPackageReview) => void,
    onCancelEdit: () => void,
}) {
    const [formData, setFormData] = useState(initialFormState)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [imageFile, setImageFile] = useState<File | null>(null)

    const isEditing = editingItem !== null

    useEffect(() => {
        if (isEditing && editingItem) {
            setFormData({
                id: editingItem.id,
                packageId: editingItem.packageId,
                name: editingItem.name,
                email: editingItem.email,
                rating: editingItem.rating,
                comment: editingItem.comment,
                reviewImage: editingItem.reviewImage || "",
                published: editingItem.published ?? false,
            })
            setImageFile(null)
        } else {
            setFormData(initialFormState)
            setImageFile(null)
        }
    }, [editingItem, isEditing])

    const handleInput = (field: keyof typeof initialFormState, value: string | boolean) => {
        setFormData(prev => ({ ...prev, [field]: value }))
    }

    // const handleSubmit = (e: FormEvent) => {
    //     e.preventDefault()
    //     setIsSubmitting(true)
    //     try {
    //         if (isEditing && editingItem) {
    //             onUpdate({ ...editingItem, ...formData })
    //         } else {
    //             onAdd({
    //                 ...formData,
    //                 reviewImage: formData.reviewImage || "",
    //             })
    //         }
    //         setFormData(initialFormState)
    //         setImageFile(null)
    //     } finally {
    //         setIsSubmitting(false)
    //     }
    // }

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)
        try {
            const submissionData = {
                ...formData,
                rating: Number(formData.rating) || 0,
                reviewImage: formData.reviewImage || "",
            }

            if (isEditing && editingItem) {
                onUpdate({
                    ...editingItem,
                    ...submissionData
                })
            } else {
                onAdd(submissionData)
            }
            setFormData(initialFormState)
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
                    {isEditing ? 'Edit Review' : 'Add Review'}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">

                    <div>
                        <Label htmlFor="name">Name</Label>
                        <Input
                            id="name"
                            value={formData.name}
                            onChange={e => handleInput('name', e.target.value)}
                            required
                        />
                    </div>

                    <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                            id="email"
                            value={formData.email}
                            onChange={e => handleInput('email', e.target.value)}
                            required
                        />
                    </div>

                    <div>
                        <Label htmlFor="rating">Rating</Label>
                        <Input
                            id="rating"
                            value={formData.rating}
                            onChange={e => handleInput('rating', e.target.value)}
                            placeholder="e.g. 5"
                            required
                        />
                    </div>

                    <div>
                        <Label htmlFor="comment">Comment</Label>
                        <Input
                            id="comment"
                            value={formData.comment}
                            onChange={e => handleInput('comment', e.target.value)}
                            required
                        />
                    </div>

                    <div>
                        <ImageUploadSingleWithMutation
                            label="Review Image"
                            initialUrl={formData.reviewImage || ''}
                            onUploaded={(url) => handleInput('reviewImage', url)}
                        />
                    </div>

                    <div>
                        <label>
                            <input
                                type="checkbox"
                                checked={formData.published}
                                onChange={e => handleInput('published', e.target.checked)}
                            /> Publish
                        </label>
                    </div>

                    <div className="flex justify-end gap-2 pt-4 border-t">
                        {isEditing && (
                            <Button type="button" variant="outline" onClick={onCancelEdit} disabled={isSubmitting}>
                                <X className="w-4 h-4" /> Cancel
                            </Button>
                        )}
                        <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? 'Saving...' : isEditing ? 'Save Changes' : 'Add Review'}
                        </Button>
                    </div>

                </form>
            </CardContent>
        </Card>
    )
}
