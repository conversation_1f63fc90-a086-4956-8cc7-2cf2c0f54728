'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { useGetPackageGroupPrices } from '@/modules/package-group-price/queries/get-package-group-prices'
import { useCreatePackageGroupPrice } from '@/modules/package-group-price/mutations/create-package-group-price'
import { useUpdatePackageGroupPrice } from '@/modules/package-group-price/mutations/update-package-group-price'
import { useDeletePackageGroupPrice } from '@/modules/package-group-price/mutations/delete-package-group-price'
import { IPackageGroupPrice } from '@/types/package_'
import { toast } from 'sonner'
import { AddGroupDiscountForm } from '@/modules/product/component/add-groupdiscount'
import { GroupDiscountList } from '@/modules/product/component/list-group-discount'


export default function EditGroupDiscountPage() {
  const { id: packageId } = useParams() as { id: string }

  const { data, isLoading, isError } = useGetPackageGroupPrices()

  const createMutation = useCreatePackageGroupPrice()
  const updateMutation = useUpdatePackageGroupPrice(packageId)
  const deleteMutation = useDeletePackageGroupPrice(packageId)

  const [items, setItems] = useState<IPackageGroupPrice[]>([])
  const [editingItem, setEditingItem] = useState<IPackageGroupPrice | null>(null)

  useEffect(() => {
    if (data?.data) {
      const groupPrices: IPackageGroupPrice[] = Array.isArray(data.data)
        ? data.data.filter(Boolean)
        : [data.data]
      setItems(groupPrices)
    }
  }, [data])

  const onAdd = (newItem: Omit<IPackageGroupPrice, 'id' | 'createdAt' | 'updatedAt'>) => {
    createMutation.mutate(
      { ...newItem, packageId },
      {
        onSuccess: res => {
          if (res.data) {
            setItems(prev => [...prev, res.data!])
            toast.success('Group discount added successfully')
          }
        },
        onError: (error) => {
          console.error('Create failed:', error)
          toast.error('Failed to add group discount')
        },
      }
    )
  }

  const onUpdate = (updatedItem: IPackageGroupPrice) => {
    updateMutation.mutate(
      updatedItem,
      {
        onSuccess: () => {
          setEditingItem(null)
          toast.success('Group discount updated successfully')
        },
        onError: (error) => {
          console.error('Update failed:', error)
          toast.error('Failed to update group discount')
        },
      }
    )
  }

  const onDelete = (id: string) => {
    deleteMutation.mutate(id, {
      onSuccess: () => toast.success('Group discount deleted successfully'),
      onError: () => toast.error('Failed to delete group discount'),
    })
  }

  const onEdit = (id: string) => {
    const foundItem = items.find(i => String(i.id) === String(id))
    setEditingItem(foundItem || null)
  }

  if (isLoading) return <div>Loading group discounts...</div>
  if (isError) return <div>Error loading group discount data.</div>

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddGroupDiscountForm
          editingItem={editingItem}
          onAdd={onAdd}
          onUpdate={onUpdate}
          onCancelEdit={() => setEditingItem(null)}
        />
        <GroupDiscountList
          items={items}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </div>
    </div>
  )
}