'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { useGetPackageFaqs } from '@/modules/package-faq/queries/get-package-faqs'
import { useCreatePackageFaq } from '@/modules/package-faq/mutations/create-package-faq'
import { useUpdatePackageFaq } from '@/modules/package-faq/mutations/update-package-faq'
import { useDeletePackageFaq } from '@/modules/package-faq/mutations/delete-package-faq'
import { IPackageFaq } from '@/types/package_'
import { toast } from 'sonner'
import { AddFAQForm } from '@/modules/product/component/add-faq-form'
import { FAQList } from '@/modules/product/component/list-faq'

export default function EditFAQPage() {
  const { id: packageId } = useParams() as { id: string }

  const { data, isLoading, isError } = useGetPackageFaqs()

  const createMutation = useCreatePackageFaq()
  const updateMutation = useUpdatePackageFaq(packageId)
  const deleteMutation = useDeletePackageFaq(packageId)

  const [items, setItems] = useState<IPackageFaq[]>([])
  const [editingItem, setEditingItem] = useState<IPackageFaq | null>(null)

  useEffect(() => {
    if (data?.data) {
      const faqs: IPackageFaq[] = Array.isArray(data.data)
        ? data.data.filter(Boolean)
        : [data.data]
      setItems(faqs)
    }
  }, [data])

  const onAdd = (newItem: Omit<IPackageFaq, 'id' | 'createdAt' | 'updatedAt'>) => {
    createMutation.mutate(
      { ...newItem, packageId },
      {
        onSuccess: res => {
          if (res.data) {
            setItems(prev => [...prev, res.data!])
            toast.success('FAQ added successfully')
          }
        },
        onError: (error) => {
          console.error('Create failed:', error)
          toast.error('Failed to add FAQ')
        },
      }
    )
  }

  const onUpdate = (updatedItem: IPackageFaq) => {
    updateMutation.mutate(
      updatedItem,
      {
        onSuccess: () => {
          setEditingItem(null)
          toast.success('FAQ updated successfully')
        },
        onError: (error) => {
          console.error('Update failed:', error)
          toast.error('Failed to update FAQ')
        },
      }
    )
  }

  const onDelete = (id: string) => {
    deleteMutation.mutate(id, {
      onSuccess: () => toast.success('FAQ deleted successfully'),
      onError: () => toast.error('Failed to delete FAQ'),
    })
  }

  const onEdit = (id: string) => {
    const foundItem = items.find(i => String(i.id) === String(id))
    setEditingItem(foundItem || null)
  }

  if (isLoading) return <div>Loading FAQs...</div>
  if (isError) return <div>Error loading FAQ data.</div>

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddFAQForm
          editingItem={editingItem}
          onAdd={onAdd}
          onUpdate={onUpdate}
          onCancelEdit={() => setEditingItem(null)}
        />
        <FAQList
          items={items}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </div>
    </div>
  )
}