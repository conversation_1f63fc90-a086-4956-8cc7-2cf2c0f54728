"use client";

import React, { useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useCreateRegion } from "@/modules/region/mutation/use-create-region";

const CreateActivity: React.FC = () => {
  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");

  const createActivity = useCreateRegion();
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !slug) {
      toast.error("Name and slug are required");
      return;
    }

    createActivity.mutate(
      { name, slug } as any,
      {
        onSuccess: () => {
          toast.success("Activity created successfully!");
          router.push("/trek");
        },
        onError: (err: Error) => {
          toast.error(`Failed to create activity: ${err.message}`);
        },
      }
    );
  };

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow">
      <h1 className="text-2xl font-bold mb-6">Create New Activity</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block mb-1 font-medium">
            Name
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full border rounded px-3 py-2"
            required
          />
        </div>
        <div>
          <label htmlFor="slug" className="block mb-1 font-medium">
            Slug
          </label>
          <input
            id="slug"
            type="text"
            value={slug}
            onChange={(e) => setSlug(e.target.value)}
            className="w-full border rounded px-3 py-2"
            required
          />
        </div>
        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
        >
          Create
        </button>
      </form>
    </div>
  );
};

export default CreateActivity;


// "use client"
// import { CategoryForm, CategoryFormData } from "@/components/category/category-form";
// import { useRouter } from "next/navigation";
// import React from "react";

// export default function CreateTrekCategory() {
//   const router = useRouter();
//   const handleSubmit = (data: CategoryFormData) => {
//     console.log("Tour category data:", data);
//     router.push("/trek");

//   };

//   return (
//     <CategoryForm
//       title="Add Tour Category"
//       onSubmit={handleSubmit}
//       cancelPath="/trek"
//     />
//   );
// }
