'use client'

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { HighlightSection } from './package-detail/highlight-section'
import { DescriptionSection } from './package-detail/description-section'
import { ShortItinerarySection } from './package-detail/short-itinerary-section'
import { IncludesSection } from './package-detail/include-section'
import { ExcludesSection } from './package-detail/exclude-section'
import { MapSection } from './package-detail/map-section'
import { VideoSection } from './package-detail/review-video'
import { IPackage } from '@/types/package_'

type HighlightSectionType = 'highlights' | 'description' | 'shortItinerary' | 'photo' | 'video' | 'includes' | 'excludes' | 'map' | 'tripInfo'

interface TripHighlightsContentProps {
  activeHighlight: HighlightSectionType
  packageData: Partial<IPackage>
  onContentChange: (field: keyof IPackage, value: any) => void
}

export function TripHighlightsContent({ 
  activeHighlight, 
  packageData, 
  onContentChange 
}: TripHighlightsContentProps) {

  // Highlights Section Handlers
  const handleHighlightsChange = (field: 'title' | 'description', value: string) => {
    const updatedHighlights = {
      ...packageData.highlights,
      [field]: value
    }
    onContentChange('highlights', updatedHighlights)
  }

  // Description Section Handlers
  const handleDescriptionChange = (field: 'title' | 'description', value: string) => {
    const updatedDescription = {
      ...packageData.description,
      [field]: value
    }
    onContentChange('description', updatedDescription)
  }

  // Short Itinerary Handlers
  const handleShortItineraryTitleChange = (value: string) => {
    const updatedShortItinerary = {
      ...packageData.shortItinerary,
      title: value
    }
    onContentChange('shortItinerary', updatedShortItinerary)
  }

  const addShortItineraryItem = () => {
    const currentPoints = packageData.shortItinerary?.points || []
    const updatedShortItinerary = {
      ...packageData.shortItinerary,
      points: [...currentPoints, ""]
    }
    onContentChange('shortItinerary', updatedShortItinerary)
  }

  const removeShortItineraryItem = (idx: number) => {
    const currentPoints = packageData.shortItinerary?.points || []
    const updatedShortItinerary = {
      ...packageData.shortItinerary,
      points: currentPoints.filter((_, i) => i !== idx)
    }
    onContentChange('shortItinerary', updatedShortItinerary)
  }

  const updateShortItineraryItem = (idx: number, text: string) => {
    const currentPoints = packageData.shortItinerary?.points || []
    const newPoints = [...currentPoints]
    newPoints[idx] = text
    const updatedShortItinerary = {
      ...packageData.shortItinerary,
      points: newPoints
    }
    onContentChange('shortItinerary', updatedShortItinerary)
  }

  // Photo Section Handlers
  const handlePhotoTitleChange = (value: string) => {
    const updatedGallery = {
      ...packageData.gallery,
      title: value
    }
    onContentChange('gallery', updatedGallery)
  }

  // Video Section Handlers
  const handleVideoTitleChange = (value: string) => {
    const updatedYtVideo = {
      ...packageData.ytVideo,
      title: value
    }
    onContentChange('ytVideo', updatedYtVideo)
  }

  const addYoutubeLink = () => {
    const currentLinks = packageData.ytVideo?.links || []
    const updatedYtVideo = {
      ...packageData.ytVideo,
      links: [...currentLinks, ""]
    }
    onContentChange('ytVideo', updatedYtVideo)
  }

  const removeYoutubeLink = (idx: number) => {
    const currentLinks = packageData.ytVideo?.links || []
    const updatedYtVideo = {
      ...packageData.ytVideo,
      links: currentLinks.filter((_, i) => i !== idx)
    }
    onContentChange('ytVideo', updatedYtVideo)
  }

  const updateYoutubeLink = (idx: number, url: string) => {
    const currentLinks = packageData.ytVideo?.links || []
    const newLinks = [...currentLinks]
    newLinks[idx] = url
    const updatedYtVideo = {
      ...packageData.ytVideo,
      links: newLinks
    }
    onContentChange('ytVideo', updatedYtVideo)
  }

  // Includes Section Handlers
  const handleIncludesChange = (field: 'title' | 'details', value: string) => {
    const updatedInclusions = {
      ...packageData.inclusions,
      [field]: value
    }
    onContentChange('inclusions', updatedInclusions)
  }

  // Excludes Section Handlers
  const handleExcludesChange = (field: 'title' | 'details', value: string) => {
    const updatedExclusions = {
      ...packageData.exclusions,
      [field]: value
    }
    onContentChange('exclusions', updatedExclusions)
  }

  // Map Section Handlers
  const handleMapTitleChange = (value: string) => {
    const updatedMap = {
      ...packageData.map,
      title: value
    }
    onContentChange('map', updatedMap)
  }

  // Trip Info Section Handlers
  const handleTripInfoTitleChange = (value: string) => {
    const updatedInfo = {
      ...packageData.info,
      title: value
    }
    onContentChange('info', updatedInfo)
  }

  if (!packageData) {
    return (
      <Card>
        <CardContent className="space-y-6 pt-6">
          <p className="text-center text-gray-500 py-8">
            Loading package content...
          </p>
        </CardContent>
      </Card>
    )
  }

  const renderSection = () => {
    switch (activeHighlight) {
      case 'highlights':
        return (
          <HighlightSection 
            title={packageData.highlights?.title || ''}
            onTitleChange={(value) => handleHighlightsChange('title', value)}
            body={packageData.highlights?.description || ''}
            onBodyChange={(value) => handleHighlightsChange('description', value)}
          />
        )
        
      case 'description':
        return (
          <DescriptionSection 
            title={packageData.description?.title || ''}
            onTitleChange={(value) => handleDescriptionChange('title', value)}
            body={packageData.description?.description || ''}
            onBodyChange={(value) => handleDescriptionChange('description', value)}
          />
        )
        
      case 'shortItinerary':
        return (
          <ShortItinerarySection
            title={packageData.shortItinerary?.title || ''}
            onTitleChange={handleShortItineraryTitleChange}
            items={packageData.shortItinerary?.points || []}
            onAddItem={addShortItineraryItem}
            onUpdateItem={updateShortItineraryItem}
            onRemoveItem={removeShortItineraryItem}
          />
        )
        
      case 'video':
        return (
          <VideoSection
            title={packageData.ytVideo?.title || ''}
            onTitleChange={handleVideoTitleChange}
            links={packageData.ytVideo?.links || []}
            onAddLink={addYoutubeLink}
            onUpdateLink={updateYoutubeLink}
            onRemoveLink={removeYoutubeLink}
          />
        )
        
      case 'includes':
        return (
          <IncludesSection 
            title={packageData.inclusions?.title || ''}
            onTitleChange={(value) => handleIncludesChange('title', value)}
            body={packageData.inclusions?.details || ''}
            onBodyChange={(value) => handleIncludesChange('details', value)}
          />
        )
        
      case 'excludes':
        return (
          <ExcludesSection 
            title={packageData.exclusions?.title || ''}
            onTitleChange={(value) => handleExcludesChange('title', value)}
            body={packageData.exclusions?.details || ''}
            onBodyChange={(value) => handleExcludesChange('details', value)}
          />
        )
        
      case 'map':
        return (
          <MapSection 
            title={packageData.map?.title || ''}
            onTitleChange={handleMapTitleChange}
            onFileChange={(file) => {/* handle file upload */}}
          />
        )
        
      default:
        return (
          <p className="text-center text-gray-500 py-8">
            Select a section from the sidebar to begin editing.
          </p>
        )
    }
  }

  return (
    <Card>
      <CardContent className="space-y-6 pt-6">
        {renderSection()}
      </CardContent>
    </Card>
  )
}
